#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强功能集成模块
"""

from typing import List, Dict, Optional, Any
from pathlib import Path

from .logger import get_logger
from .exceptions import VideoEditorError, handle_exception
from ..media_processing.audio_processor import AudioProcessor
from ..media_processing.ocr_processor import OCRProcessor
# AutoSyncProcessor removed - using basic sync functionality only


class EnhancedFeatureManager:
    """增强功能管理器"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_logger('enhanced_features')
        
        # 初始化处理器
        self.audio_processor = AudioProcessor(config)
        self.ocr_processor = OCRProcessor(config)
        # auto_sync_processor removed - using basic sync functionality only
        self.auto_sync_processor = None
        
        self.logger.info("Enhanced features initialized")
    
    @handle_exception
    def analyze_video_content(self, video_path: str) -> Dict[str, Any]:
        """全面分析视频内容"""
        try:
            self.logger.info(f"Starting comprehensive video analysis: {video_path}")
            
            analysis_result = {
                'video_path': video_path,
                'audio_features': None,
                'text_content': None,
                'music_structure': None,
                'recommendations': []
            }
            
            # 1. 音频分析
            try:
                # 从视频提取音频
                temp_audio = Path(self.config.get_temp_dir()) / f"temp_audio_{Path(video_path).stem}.wav"
                if self.audio_processor.extract_audio_from_video(video_path, str(temp_audio)):
                    # 分析音频特征
                    audio_features = self.audio_processor.analyze_audio_features(str(temp_audio))
                    analysis_result['audio_features'] = audio_features
                    
                    # 分析音乐结构
                    music_structure = self.auto_sync_processor.analyze_music_structure(str(temp_audio))
                    analysis_result['music_structure'] = music_structure
                    
                    self.logger.info(f"Audio analysis completed: tempo={audio_features.get('tempo', 0):.1f} BPM")
                    
                    # 清理临时文件
                    if temp_audio.exists():
                        temp_audio.unlink()
                        
            except Exception as e:
                self.logger.warning(f"Audio analysis failed: {str(e)}")
                analysis_result['recommendations'].append("音频分析失败，建议检查音频质量")
            
            # 2. 文字识别
            try:
                text_frames = self.ocr_processor.extract_text_from_video(video_path, sample_interval=2.0)
                if text_frames:
                    merged_texts = self.ocr_processor.merge_similar_text_regions(text_frames)
                    analysis_result['text_content'] = {
                        'frames': text_frames,
                        'merged_texts': merged_texts,
                        'total_text_regions': len(merged_texts)
                    }
                    self.logger.info(f"OCR analysis completed: found {len(merged_texts)} unique text regions")
                else:
                    analysis_result['recommendations'].append("未检测到文字内容")
                    
            except Exception as e:
                self.logger.warning(f"OCR analysis failed: {str(e)}")
                analysis_result['recommendations'].append("文字识别失败，建议检查视频清晰度")
            
            # 3. 生成建议
            self._generate_recommendations(analysis_result)
            
            self.logger.info("Comprehensive video analysis completed")
            return analysis_result
            
        except Exception as e:
            raise VideoEditorError(f"Video content analysis failed: {str(e)}")
    
    def _generate_recommendations(self, analysis_result: Dict[str, Any]):
        """生成优化建议"""
        recommendations = analysis_result['recommendations']
        
        # 音频相关建议
        audio_features = analysis_result.get('audio_features')
        if audio_features:
            tempo = audio_features.get('tempo', 0)
            if tempo < 80:
                recommendations.append("音乐节奏较慢，建议使用较长的视频片段")
            elif tempo > 140:
                recommendations.append("音乐节奏较快，建议使用较短的视频片段")
            
            energy = audio_features.get('energy', 0)
            if energy < 0.3:
                recommendations.append("音频能量较低，建议增加音量或选择更有活力的背景音乐")
            
        # 文字内容建议
        text_content = analysis_result.get('text_content')
        if text_content and text_content['total_text_regions'] > 0:
            recommendations.append(f"检测到{text_content['total_text_regions']}个文字区域，可考虑添加字幕或文字效果")
        
        # 音乐结构建议
        music_structure = analysis_result.get('music_structure')
        if music_structure:
            segments = music_structure.get('music_segments', [])
            if len(segments) > 1:
                recommendations.append(f"检测到{len(segments)}个音乐段落，建议根据音乐结构安排视频片段")
    
    @handle_exception
    def auto_sync_video_to_music(self, video_segments: List[Dict[str, Any]],
                                audio_path: str) -> List[Dict[str, Any]]:
        """自动将视频片段同步到音乐 - 基础实现"""
        try:
            self.logger.info("Starting basic sync process")

            # 基础同步实现 - 返回原始片段
            self.logger.warning("Advanced auto-sync features removed - returning original segments")
            return video_segments
            
            # 应用自动同步
            synced_segments = self.auto_sync_processor.apply_auto_sync(segments, sync_points)
            
            # 优化片段时长
            optimized_segments = self.auto_sync_processor.optimize_segment_timing(
                synced_segments, music_structure
            )
            
            # 转换回字典格式
            result_segments = []
            for seg in optimized_segments:
                result_segments.append({
                    'id': seg.id,
                    'start_time': seg.start_time,
                    'duration': seg.duration,
                    'end_time': seg.end_time,
                    'file_path': seg.file_path,
                    'segment_type': seg.segment_type,
                    'priority': seg.priority,
                    'synced': True
                })
            
            self.logger.info(f"Auto-sync completed for {len(result_segments)} segments")
            return result_segments
            
        except Exception as e:
            raise VideoEditorError(f"Auto-sync failed: {str(e)}")
    
    @handle_exception
    def extract_video_text(self, video_path: str, output_format: str = 'srt') -> str:
        """提取视频中的文字并生成字幕文件"""
        try:
            self.logger.info(f"Extracting text from video: {video_path}")
            
            # 提取文字
            text_frames = self.ocr_processor.extract_text_from_video(video_path)
            
            if not text_frames:
                return ""
            
            # 合并相似文字
            merged_texts = self.ocr_processor.merge_similar_text_regions(text_frames)
            
            # 生成字幕文件内容
            if output_format.lower() == 'srt':
                subtitle_content = self._generate_srt_content(merged_texts)
            elif output_format.lower() == 'vtt':
                subtitle_content = self._generate_vtt_content(merged_texts)
            else:
                subtitle_content = self._generate_txt_content(merged_texts)
            
            self.logger.info(f"Text extraction completed: {len(merged_texts)} text segments")
            return subtitle_content
            
        except Exception as e:
            raise VideoEditorError(f"Text extraction failed: {str(e)}")
    
    def _generate_srt_content(self, merged_texts: List[Dict[str, Any]]) -> str:
        """生成SRT格式字幕"""
        srt_content = []
        
        for i, text_info in enumerate(merged_texts, 1):
            start_time = text_info['first_seen']
            end_time = text_info['last_seen']
            text = text_info['text']
            
            # 格式化时间
            start_srt = self._seconds_to_srt_time(start_time)
            end_srt = self._seconds_to_srt_time(end_time)
            
            srt_content.append(f"{i}")
            srt_content.append(f"{start_srt} --> {end_srt}")
            srt_content.append(text)
            srt_content.append("")  # 空行
        
        return "\n".join(srt_content)
    
    def _generate_vtt_content(self, merged_texts: List[Dict[str, Any]]) -> str:
        """生成VTT格式字幕"""
        vtt_content = ["WEBVTT", ""]
        
        for text_info in merged_texts:
            start_time = text_info['first_seen']
            end_time = text_info['last_seen']
            text = text_info['text']
            
            # 格式化时间
            start_vtt = self._seconds_to_vtt_time(start_time)
            end_vtt = self._seconds_to_vtt_time(end_time)
            
            vtt_content.append(f"{start_vtt} --> {end_vtt}")
            vtt_content.append(text)
            vtt_content.append("")  # 空行
        
        return "\n".join(vtt_content)
    
    def _generate_txt_content(self, merged_texts: List[Dict[str, Any]]) -> str:
        """生成纯文本格式"""
        txt_content = []
        
        for text_info in merged_texts:
            start_time = text_info['first_seen']
            text = text_info['text']
            
            txt_content.append(f"[{start_time:.2f}s] {text}")
        
        return "\n".join(txt_content)
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def _seconds_to_vtt_time(self, seconds: float) -> str:
        """将秒数转换为VTT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        
        return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"
    
    @handle_exception
    def get_sync_recommendations(self, video_segments: List[Dict[str, Any]], 
                               audio_path: str) -> Dict[str, Any]:
        """获取同步建议"""
        try:
            # 分析音乐结构
            music_structure = self.auto_sync_processor.analyze_music_structure(audio_path)
            
            recommendations = {
                'tempo': music_structure['features']['tempo'],
                'time_signature': music_structure['beat_pattern']['time_signature'],
                'total_beats': len(music_structure['beat_points']),
                'strong_beats': len(music_structure['strong_beats']),
                'music_segments': len(music_structure['music_segments']),
                'suggestions': []
            }
            
            # 生成具体建议
            tempo = recommendations['tempo']
            if tempo < 90:
                recommendations['suggestions'].append("慢节奏音乐，建议使用2-4秒的视频片段")
            elif tempo > 130:
                recommendations['suggestions'].append("快节奏音乐，建议使用1-2秒的视频片段")
            else:
                recommendations['suggestions'].append("中等节奏音乐，建议使用1.5-3秒的视频片段")
            
            # 分析视频片段与音乐的匹配度
            total_video_duration = sum(seg.get('duration', 0) for seg in video_segments)
            audio_duration = music_structure['features']['duration']
            
            if total_video_duration > audio_duration * 1.2:
                recommendations['suggestions'].append("视频内容过多，建议删减部分片段")
            elif total_video_duration < audio_duration * 0.8:
                recommendations['suggestions'].append("视频内容不足，建议添加更多片段")
            
            return recommendations
            
        except Exception as e:
            raise VideoEditorError(f"Sync recommendations failed: {str(e)}")

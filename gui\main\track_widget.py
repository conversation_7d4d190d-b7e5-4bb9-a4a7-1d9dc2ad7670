#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
from pathlib import Path
from PySide6.QtWidgets import QWidget, QLabel
from PySide6.QtCore import Qt, Signal, QPoint, QPointF
from PySide6.QtGui import QPainter, QColor, QPen, QBrush, QPolygon, QMouseEvent
from .timeline_components import TrimHandlesOverlay

class TrackWidget(QWidget):
    """轨道显示组件 - 支持跨轨道拖拽"""

    # 添加媒体块点击信号
    media_clicked = Signal(str, float)  # file_path, start_time
    media_dropped = Signal(str, int)  # file_path, drop_x

    def __init__(self, track_type: str, global_params=None):
        super().__init__()
        self.track_type = track_type

        # 使用全局参数管理器
        if global_params is None:
            # 如果没有提供全局参数，创建一个临时的（向后兼容）
            from .multi_track_timeline import TimelineGlobalParams
            self.global_params = TimelineGlobalParams()
            self._owns_global_params = True
        else:
            self.global_params = global_params
            self._owns_global_params = False

        self.setFixedHeight(self.global_params.track_height)
        self.setMinimumWidth(self.global_params.get_timeline_width())
        self.setAcceptDrops(True)
        # 设置样式 - 现代化主题
        color = "#4CAF50" if track_type == "video" else "#FF9800"
        self.base_style = f"""
            TrackWidget {{
                background-color: #333333;
                border: 1px solid #444;
                border-left: 4px solid {color};
                border-radius: 4px;
                margin: 2px 0px 2px 0px;
            }}
        """
        self.drag_style = f"""
            TrackWidget {{
                background-color: #444444;
                border: 2px dashed #00D5FF;
                border-left: 4px solid {color};
                border-radius: 4px;
                margin: 2px 0px 2px 0px;
            }}
        """
        self.setStyleSheet(self.base_style)

        # 拖动状态
        self.drag_position = -1
        self.dragging = False
        self.drag_active = False  # 是否处于拖拽激活状态

        # 媒体片段列表
        self.media_items = []

        # 连接全局参数变化信号
        self.global_params.zoom_changed.connect(self.on_zoom_changed)
        self.global_params.duration_changed.connect(self.on_duration_changed)

        # 创建专门的游标绘制层
        print(f"🔧 TrackWidget 创建 TrimHandlesOverlay")
        self.trim_handles_overlay = TrimHandlesOverlay(self)
        self.trim_handles_overlay.setGeometry(0, 0, self.width(), self.height())
        print(f"🔧 TrimHandlesOverlay 创建完成，几何: {self.trim_handles_overlay.geometry()}")

        print(f"✅ TrackWidget ({track_type}) 初始化完成，使用全局参数管理器")

    def on_zoom_changed(self, pixels_per_second: float):
        """响应缩放变化"""
        self.setMinimumWidth(self.global_params.get_timeline_width())
        self.update_display()

    def on_duration_changed(self, duration: float):
        """响应时长变化"""
        self.setMinimumWidth(self.global_params.get_timeline_width())

    def set_drag_active(self, active: bool):
        """设置拖拽激活状态"""
        self.drag_active = active
        if active:
            self.setStyleSheet(self.drag_style)
        else:
            self.setStyleSheet(self.base_style)

    def set_drag_position(self, x_pos: int):
        """设置拖动预览位置"""
        # 只有位置真正改变时才更新
        if self.drag_position != x_pos:
            old_pos = self.drag_position
            self.drag_position = x_pos

            # 只更新受影响的区域，减少闪烁
            if old_pos >= 0:
                # 清除旧位置的绘制区域
                self.update(old_pos - 10, 0, 20, self.height())
            if x_pos >= 0:
                # 更新新位置的绘制区域
                self.update(x_pos - 10, 0, 20, self.height())

    def clear_drag_position(self):
        """清除拖动预览位置"""
        if self.drag_position >= 0:
            old_pos = self.drag_position
            self.drag_position = -1
            # 只更新之前绘制的区域
            self.update(old_pos - 10, 0, 20, self.height())
        else:
            self.drag_position = -1

    def add_media_item(self, media_item):
        """添加媒体片段"""
        self.media_items.append(media_item)
        self.update_display()

    def set_zoom(self, pixels_per_second: float):
        """设置缩放级别 - 委托给全局参数管理器"""
        self.global_params.set_pixels_per_second(pixels_per_second)

    def mousePressEvent(self, event):
        """鼠标点击事件 - 优先处理媒体块的裁剪功能，然后处理轨道点击"""
        # 🔧 修复：首先检查是否点击了媒体块的裁剪游标
        # 获取点击位置下的子组件
        child_at_pos = self.childAt(event.position().toPoint())

        # 如果点击了媒体块，让媒体块优先处理事件（包括裁剪游标）
        if child_at_pos and hasattr(child_at_pos, 'mousePressEvent'):
            # 将事件坐标转换为子组件的坐标系
            child_pos = child_at_pos.mapFromParent(event.position().toPoint())
            child_event = QMouseEvent(
                event.type(),
                QPointF(child_pos),
                event.globalPosition(),
                event.button(),
                event.buttons(),
                event.modifiers()
            )

            # 让子组件处理事件
            child_at_pos.mousePressEvent(child_event)

            # 如果子组件处理了事件（比如开始裁剪），就不再处理轨道点击
            if hasattr(child_at_pos, 'left_trim_dragging') and child_at_pos.left_trim_dragging:
                return
            if hasattr(child_at_pos, 'right_trim_dragging') and child_at_pos.right_trim_dragging:
                return
            if hasattr(child_at_pos, 'drag_start_pos') and child_at_pos.drag_start_pos:
                return

        # 🔧 修复：清除任何拖动预览线
        self.clear_drag_position()

        # 查找父级MultiTrackTimeline来清除所有拖动指示器
        timeline = self.parent()
        while timeline and not hasattr(timeline, 'clear_all_drag_indicators'):
            timeline = timeline.parent()
        if timeline:
            timeline.clear_all_drag_indicators()

        # 🔧 修改点击逻辑：只有在没有媒体块处理事件时才处理轨道点击
        if event.button() == Qt.MouseButton.LeftButton:
            # 计算点击位置对应的时间
            click_x = event.position().x()
            time_pos = self.global_params.pixels_to_time(click_x)

            # 🔧 修复：查找MultiTrackTimeline类型的父级
            timeline = self.parent()
            while timeline:
                # 检查是否是MultiTrackTimeline类
                if timeline.__class__.__name__ == 'MultiTrackTimeline':
                    break
                timeline = timeline.parent()

            if timeline and hasattr(timeline, 'on_timeline_position_changed'):
                timeline.on_timeline_position_changed(time_pos)
                print(f"🎯 轨道点击，移动时间轴到: {time_pos:.2f}s")
            else:
                print(f"❌ 未找到MultiTrackTimeline或on_timeline_position_changed方法")

    def update_display(self):
        """更新显示"""
        # 清除现有的媒体块
        for child in self.findChildren(QLabel):
            child.deleteLater()

        # 重新创建媒体块
        for media_item in self.media_items:
            if isinstance(media_item, dict):
                file_path = media_item['file_path']
                start_time = media_item['start_time']
                duration = media_item['duration']
                name = media_item['name']
            else:
                # 兼容旧格式
                file_path = media_item
                start_time = 0
                duration = 10.0
                name = Path(file_path).stem

            # 计算位置和大小
            x_pos = self.global_params.time_to_pixels(start_time)
            width = max(80, self.global_params.time_to_pixels(duration))

            # 创建媒体块
            media_block = QLabel(name[:12] + "..." if len(name) > 12 else name)
            media_block.setParent(self)
            media_block.setGeometry(x_pos + 5, 0, width - 10, 64)  # 高度64px，无上下间距

            # 设置样式 - 添加鼠标悬停效果
            bg_color = "#4CAF50" if self.track_type == "video" else "#FF9800"
            media_block.setStyleSheet(f"""
                QLabel {{
                    background-color: {bg_color};
                    border: 1px solid #777;
                    border-radius: 4px;
                    color: #FFF;
                    font-size: 10px;
                    font-weight: bold;
                    padding: 2px;
                }}
                QLabel:hover {{
                    border: 2px solid #00D5FF;
                    background-color: {self._lighter_color(bg_color)};
                }}
            """)
            media_block.show()

    def _lighter_color(self, color_hex: str) -> str:
        """生成更亮的颜色"""
        if color_hex == "#4CAF50":
            return "#66BB6A"
        elif color_hex == "#FF9800":
            return "#FFB74D"
        return color_hex

    def resizeEvent(self, event):
        """处理大小变化事件"""
        super().resizeEvent(event)
        # 更新游标绘制层的大小
        if hasattr(self, 'trim_handles_overlay'):
            self.trim_handles_overlay.setGeometry(0, 0, self.width(), self.height())

    def update_trim_handles(self):
        """更新游标显示"""
        print(f"🔧 TrackWidget.update_trim_handles 被调用")
        if hasattr(self, 'trim_handles_overlay'):
            print(f"🔧 找到 trim_handles_overlay，调用更新")
            self.trim_handles_overlay.update()
        else:
            print(f"❌ 没有找到 trim_handles_overlay")

    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制轨道背景色#333333
        painter.fillRect(self.rect(), QColor(51, 51, 51))  # #333333

        # 🔧 禁用：不再绘制播放头线，使用统一播放头管理器
        # if hasattr(self, 'current_position') and self.current_position >= 0:
        #     x_pos = int(self.current_position * getattr(self, 'pixels_per_second', 100))
        #     if 0 <= x_pos <= self.width():
        #         painter.setPen(QPen(QColor(0, 200, 150), 3))  # #00c896颜色，3px宽度
        #         painter.drawLine(x_pos, 0, x_pos, self.height())
        #         # print(f"✅ TrackWidget 播放头已绘制: x={x_pos}, 高度={self.height()}")

        # 绘制拖动预览线 - 颜色#00c896，防止重影
        if self.drag_position >= 0:
            if 0 <= self.drag_position <= self.width():
                # 绘制预览线 - #00c896颜色
                painter.setPen(QPen(QColor(0, 200, 150), 3))  # #00c896颜色，3px宽度
                painter.drawLine(self.drag_position, 0, self.drag_position, self.height())

                # 绘制顶部三角形指示器
                points = [
                    QPoint(self.drag_position - 6, 0),
                    QPoint(self.drag_position + 6, 0),
                    QPoint(self.drag_position, 12)
                ]
                polygon = QPolygon(points)

                painter.setBrush(QBrush(QColor(0, 200, 150)))  # #00c896颜色
                painter.setPen(QPen(QColor(0, 200, 150), 1))
                painter.drawPolygon(polygon)

        # 重置画笔和画刷
        painter.setBrush(QBrush())
        painter.setPen(QPen())

    def dragEnterEvent(self, event):
        """拖动进入事件"""
        if event.mimeData().hasText():
            event.acceptProposedAction()
            self.dragging = True

            # 检查是否有占位符，给出不同的视觉提示
            has_placeholder = any(item.get('is_placeholder', False) for item in self.media_items)
            if has_placeholder:
                self.setStyleSheet("background-color: rgba(255, 200, 100, 80); border: 2px dashed #FFA500;")  # 橙色提示有占位符
                print("🎯 检测到占位符轨道，请拖拽到占位符位置进行替换")
            else:
                self.setStyleSheet("background-color: rgba(100, 150, 255, 50);")  # 蓝色普通拖拽

    def dragMoveEvent(self, event):
        """拖动移动事件"""
        if event.mimeData().hasText():
            # 直接设置新的拖动位置，避免频繁清除
            new_position = event.position().x()

            # 只有位置真正改变时才更新
            if not hasattr(self, '_last_drag_position') or abs(self._last_drag_position - new_position) > 2:
                self._last_drag_position = new_position
                self.set_drag_position(new_position)

                # 同步到滚动内容区域
                timeline = self.parent()
                while timeline and not hasattr(timeline, 'scroll_content'):
                    timeline = timeline.parent()
                if timeline and timeline.scroll_content:
                    timeline.scroll_content.set_drag_position(new_position)

            event.acceptProposedAction()

    def dragLeaveEvent(self, event):
        """拖动离开事件"""
        # 清除拖动状态
        self.clear_drag_position()
        self.set_drag_active(False)  # 取消激活状态
        self.dragging = False
        self.setStyleSheet("")  # 清除拖拽样式

        # 清除滚动内容区域的预览线
        timeline = self.parent()
        while timeline and not hasattr(timeline, 'scroll_content'):
            timeline = timeline.parent()
        if timeline and hasattr(timeline, 'scroll_content') and timeline.scroll_content:
            timeline.scroll_content.clear_drag_position()

        # 清除拖动位置记录
        if hasattr(self, '_last_drag_position'):
            delattr(self, '_last_drag_position')

    def dropEvent(self, event):
        """放置事件 - 处理媒体文件和媒体块拖动"""
        if event.mimeData().hasText():
            drop_x = event.position().x()

            # 检查是否是媒体块拖动
            if event.mimeData().hasFormat("application/x-media-block"):
                try:
                    drag_data = json.loads(event.mimeData().text())

                    if drag_data.get('type') == 'media_block':
                        # 这是媒体块跨轨道拖动
                        self.handle_media_block_drop(drag_data, drop_x)
                        event.acceptProposedAction()
                        return
                except Exception as e:
                    print(f"解析媒体块拖动数据失败: {e}")

            # 常规媒体文件拖动
            file_path = event.mimeData().text()

            # 🔧 修复重复调用问题：只发出信号，不直接调用handle_track_drop
            # 因为在multi_track_timeline.py中已经连接了信号到handle_track_drop
            # 发出媒体拖放信号，让信号处理器来调用handle_track_drop
            self.media_dropped.emit(file_path, int(drop_x))

            # 清除所有拖拽指示器
            # 向上查找MultiTrackTimeline实例
            timeline = self.parent()
            while timeline and not hasattr(timeline, 'clear_all_drag_indicators'):
                timeline = timeline.parent()

            if timeline and hasattr(timeline, 'clear_all_drag_indicators'):
                timeline.clear_all_drag_indicators()
            else:
                # 备用清除方法
                self.clear_drag_position()
                self.set_drag_active(False)
                self.dragging = False

            # 清除拖动位置记录
            if hasattr(self, '_last_drag_position'):
                delattr(self, '_last_drag_position')

            event.acceptProposedAction()

    def handle_media_block_drop(self, drag_data, drop_x):
        """处理媒体块跨轨道拖动"""
        media_item = drag_data['media_item']
        source_track_index = drag_data['track_index']
        source_media_index = drag_data['media_index']

        # 计算新的时间位置
        new_time = max(0, self.global_params.pixels_to_time(drop_x))

        # 应用磁性吸附
        if hasattr(timeline, 'apply_snap') and callable(timeline.apply_snap):
            new_time = timeline.apply_snap(new_time)

        # 向上查找MultiTrackTimeline实例
        timeline = self.parent()
        while timeline and not hasattr(timeline, 'tracks'):
            timeline = timeline.parent()

        if timeline:
            # 从源轨道移除媒体块
            if 0 <= source_track_index < len(timeline.tracks):
                source_track = timeline.tracks[source_track_index]
                if 0 <= source_media_index < len(source_track['media_files']):
                    source_track['media_files'].pop(source_media_index)

                    # 更新源轨道显示
                    if hasattr(timeline, 'update_track_display'):
                        timeline.update_track_display(source_track_index)

            # 更新媒体项的时间位置
            media_item['start_time'] = new_time

            # 添加到目标轨道
            target_track_index = -1
            for i, track_widget in enumerate(timeline.track_widgets):
                if track_widget == self:
                    target_track_index = i
                    break

            if target_track_index >= 0:
                target_track = timeline.tracks[target_track_index]
                target_track['media_files'].append(media_item)

                # 更新目标轨道显示
                if hasattr(timeline, 'update_track_display'):
                    timeline.update_track_display(target_track_index)

                # 更新总时长
                if hasattr(timeline, 'update_total_duration'):
                    timeline.update_total_duration()

                print(f"媒体块已跨轨道移动: {media_item['name']} 到轨道 {target_track['name']} 时间 {new_time:.1f}s")

        # 强制清除所有拖拽指示器
        if timeline and hasattr(timeline, 'clear_all_drag_indicators'):
            timeline.clear_all_drag_indicators()
        else:
            # 备用清除方法
            self.clear_drag_position()
            self.set_drag_active(False)
